<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Borges Media - Create Your Perfect Billboard</title>
    <link rel="stylesheet" href="../assets/css/customer-style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>Borgesmedia</h1>
                    <p>Billboard Designer Page</p>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Calendar Booking Section -->
    <section class="calendar-section">
        <div class="container">
            <h2>Create Billboards That Get Noticed</h2>
            <p class="section-description">Choose the dates when you want your billboard to be displayed. You must select at least one date before proceeding.</p>

            <!-- Calendar Container -->
            <div id="billboardCalendar"></div>

            <!-- Calendar Actions -->
            <div class="calendar-actions">
                <button type="button" class="btn btn-secondary" onclick="clearCalendarSelection()">Clear Selection</button>
                <button type="button" class="btn btn-primary" onclick="proceedWithDates()" id="proceedWithDatesBtn" disabled>Continue with Selected Dates</button>
            </div>
        </div>
    </section>

    <!-- Billboard Options Section -->
    <section class="hero" id="billboardOptionsSection" style="display: none;">
        <div class="container">
            <div class="hero-content">
                <div class="calendar-requirement-notice" id="dateRequirementNotice">
                    <div class="icon">📅</div>
                    <p><strong>Please select your billboard display dates above before choosing a design option.</strong></p>
                </div>

                <!-- Main Action Buttons -->
                <div class="action-buttons" id="actionButtons">
                    <div class="button-card">
                        <div class="button-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="8.5" cy="8.5" r="1.5"/>
                                <polyline points="21,15 16,10 5,21"/>
                            </svg>
                        </div>
                        <h3>Templated Billboard</h3>
                        <p>Choose from our collection of designed templates.</p>
                        <button onclick="proceedToTemplates()" class="btn btn-primary" id="templatesBtn" disabled>
                            Choose Template
                        </button>
                    </div>

                    <div class="button-card">
                        <div class="button-icon">
                            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                            </svg>
                        </div>
                        <h3>Custom Billboard</h3>
                        <p>Create a completely unique billboard tailored to your specific needs and brand identity.</p>
                        <button onclick="selectBillboardType('custom')" class="btn btn-secondary" id="customBtn" disabled>
                            Start Custom Design
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Borges Media. All rights reserved.</p>
        </div>
    </footer>

    <script src="../assets/js/calendar-booking.js"></script>
    <script src="shared/order-data-manager.js"></script>
    <script src="../assets/js/customer-script.js"></script>
</body>
</html>
